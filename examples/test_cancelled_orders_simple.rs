use backtest::backtest_summary::HtmlGenerator;
use backtest::types::{BacktestRecorder, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 测试被取消订单的显示功能");
    println!("================================");

    // 创建回测记录器
    let mut recorder = BacktestRecorder::new();
    let start_time = Utc::now();
    recorder.start_recording(start_time);

    // 添加一些BBO数据
    println!("1. 添加BBO数据...");
    for i in 0..10 {
        let bbo = backtest::types::Bbo {
            update_id: i,
            bid_price: Price::new(50000.0 + i as f64),
            bid_quantity: 1.0,
            ask_price: Price::new(50001.0 + i as f64),
            ask_quantity: 1.0,
            timestamp: Some((start_time.timestamp_micros() as u64) + i * 1000000),
            data_source_type: backtest::config::DataSourceType::BinanceOfficial,
        };
        recorder.record_bbo(&bbo);
    }
    println!("   ✓ 添加了 {} 条BBO记录", recorder.bbo_records.len());

    // 添加不同状态的订单
    println!("2. 添加不同状态的订单...");

    // 已成交订单
    let filled_order = backtest::types::Order {
        id: "order_filled".to_string(),
        client_order_id: "client_filled".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50000.0)),
        quantity: 0.1,
        status: OrderStatus::Filled,
        timestamp: start_time + chrono::Duration::seconds(1),
        execution_info: None,
    };
    recorder.record_order(&filled_order);

    // 部分成交订单
    let partial_order = backtest::types::Order {
        id: "order_partial".to_string(),
        client_order_id: "client_partial".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Sell,
        price: Some(Price::new(50002.0)),
        quantity: 0.2,
        status: OrderStatus::PartiallyFilled,
        timestamp: start_time + chrono::Duration::seconds(2),
        execution_info: None,
    };
    recorder.record_order(&partial_order);

    // 被取消订单 - 买入
    let cancelled_buy_order = backtest::types::Order {
        id: "order_cancelled_buy".to_string(),
        client_order_id: "client_cancelled_buy".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(49999.0)),
        quantity: 0.15,
        status: OrderStatus::Cancelled,
        timestamp: start_time + chrono::Duration::seconds(3),
        execution_info: None,
    };
    recorder.record_order(&cancelled_buy_order);

    // 被取消订单 - 卖出
    let cancelled_sell_order = backtest::types::Order {
        id: "order_cancelled_sell".to_string(),
        client_order_id: "client_cancelled_sell".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Sell,
        price: Some(Price::new(50010.0)),
        quantity: 0.12,
        status: OrderStatus::Cancelled,
        timestamp: start_time + chrono::Duration::seconds(5),
        execution_info: None,
    };
    recorder.record_order(&cancelled_sell_order);
    println!("   ✓ 记录了卖出取消订单: {}", cancelled_sell_order.id);

    // 被拒绝订单
    let rejected_order = backtest::types::Order {
        id: "order_rejected".to_string(),
        client_order_id: "client_rejected".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Market,
        side: OrderSide::Sell,
        price: None,
        quantity: 1.0,
        status: OrderStatus::Rejected,
        timestamp: start_time + chrono::Duration::seconds(6),
        execution_info: None,
    };
    recorder.record_order(&rejected_order);

    println!("   ✓ 添加了 {} 条订单记录", recorder.orders.len());
    println!("     - 已成交: 1");
    println!("     - 部分成交: 1");
    println!("     - 买入取消: 1");
    println!("     - 卖出取消: 1");
    println!("     - 已拒绝: 1");

    // 停止记录并生成总结
    println!("3. 生成回测总结...");
    recorder.stop_recording(Utc::now());

    if let Some(summary) = recorder.generate_summary() {
        let html = HtmlGenerator::generate_summary_html(&summary);

        // 保存HTML文件
        std::fs::write("test_cancelled_orders_report.html", html)?;
        println!("   ✓ 回测报告已生成: test_cancelled_orders_report.html");

        // 验证订单数据
        println!("4. 验证订单数据...");
        println!("   - 总订单数: {}", summary.orders.len());
        for order in &summary.orders {
            println!(
                "     {} - {:?} - {:?}",
                order.order_id, order.side, order.status
            );
        }

        println!("\n✅ 测试完成！请打开 test_cancelled_orders_report.html 查看结果");
        println!("   应该能看到：");
        println!("   - K线图上显示不同颜色和形状的订单标记");
        println!("   - 订单表格中显示所有订单（包括被取消的）");
        println!("   - 被取消的订单有特殊的背景色");
    } else {
        println!("❌ 无法生成回测总结");
    }

    Ok(())
}
